<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guía de Hilos - Tu Imagen Personalizada</title>
    <link rel="stylesheet" href="/thread_viewer/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <div class="header">
        <h1><i class="fas fa-thread"></i> Tu Imagen de Hilos Personalizada</h1>
        <p class="subtitle">Guía paso a paso para recrear tu imagen con el kit de hilos</p>
    </div>

    <div class="instructions-panel">
        <div class="instructions-content">
            <h3><i class="fas fa-info-circle"></i> Instrucciones</h3>
            <ul>
                <li><strong>Paso a paso:</strong> Usa los botones o las flechas del teclado para navegar</li>
                <li><strong>Círculo dividido:</strong> Cada cuarto representa una sección de tu tablero circular</li>
                <li><strong>Números:</strong> Indican el pin específico donde debes colocar el hilo</li>
                <li><strong>Secuencia:</strong> Sigue el orden exacto para recrear tu imagen</li>
            </ul>
        </div>
    </div>

    <div class="thread-viewer-container">
        <div id="progress-counter" class="progress-counter">
            Paso 0 de 0
        </div>
        <div class="donut-container">
            <div class="donut">
                <div class="quarter q1">
                    <div class="quarter-circle q1">
                        <span class="quarter-number q1"></span>
                    </div>
                </div>
                <div class="quarter q2">
                    <div class="quarter-circle q2">
                        <span class="quarter-number q2"></span>
                    </div>
                </div>
                <div class="quarter q3">
                    <div class="quarter-circle q3">
                        <span class="quarter-number q3"></span>
                    </div>
                </div>
                <div class="quarter q4">
                    <div class="quarter-circle q4">
                        <span class="quarter-number q4"></span>
                    </div>
                </div>
            </div>
        </div>
        <div id="navigation" class="navigation">
            <button id="prev-btn" class="nav-btn nav-btn-prev">
                <span class="btn-icon">←</span>
                Anterior
            </button>
            <button id="next-btn" class="nav-btn nav-btn-next">
                Siguiente
                <span class="btn-icon">→</span>
            </button>
        </div>
    </div>
    <script src="/thread_viewer/script.js"></script>
</body>

</html>