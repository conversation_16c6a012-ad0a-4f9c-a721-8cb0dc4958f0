/* /home/<USER>/personalDev/hilos/static/styles.css */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --background-color: #f4f4f4;
    --text-color: #333;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

header {
    text-align: center;
    margin-bottom: 2rem;
}

header h1 {
    color: var(--primary-color);
    font-weight: 700;
}

header h1 span {
    display: block;
    font-size: 0.6em;
    color: var(--secondary-color);
}

.image-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
}

.file-input {
    display: none;
}

.file-input-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 1rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.file-input-label:hover {
    background-color: #2980b9;
}

.file-input-label i {
    margin-right: 0.5rem;
    font-size: 1.5rem;
}

.preview-container {
    margin-top: 1rem;
    max-width: 100%;
    text-align: center;
}

.preview-image {
    max-width: 300px;
    max-height: 300px;
    object-fit: contain;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.setting {
    display: flex;
    flex-direction: column;
    width: 48%;
}

.setting label {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.setting label i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.setting input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.setting select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 1rem;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath d='M1 4l5 5 5-5z' fill='%23999999'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem top 50%;
    background-size: 8px;
}

.setting select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.generate-btn {
    width: 100%;
    padding: 1rem;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.generate-btn:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

.generate-btn:not(:disabled):hover {
    background-color: #27ae60;
}

.generate-btn i {
    margin-right: 0.5rem;
}

.status-container {
    margin: 1rem 0;
    text-align: center;
}

.loading {
    display: none;
    color: var(--primary-color);
}

.error {
    color: red;
    font-weight: bold;
}

.result-container {
    margin-top: 1.5rem;
    text-align: center;
}

.result-image {
    max-width: 100%;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

.progress-bar-container {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-bar {
    width: 0%;
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.5s ease-in-out;
}

/* Estilos generales para el formulario de pago */
#payment-form {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
}

/* Estilo para el elemento de tarjeta */
#card-element {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 10px;
}

/* Estilo para los errores de tarjeta */
#card-errors {
    color: #fa755a;
    margin-top: 10px;
}

/* Estilo para el botón de pago */
.pay-btn {
    background-color: #4CAF50;
    /* Verde */
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.pay-btn:hover {
    background-color: #45a049;
    /* Verde más oscuro */
}

/* Estilos para el botón de compra */
.buy-btn {
    display: block;
    margin: 20px auto;
    padding: 15px 30px;
    background-color: #2ecc71;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1.1em;
    transition: background-color 0.3s ease;
}

.buy-btn:hover {
    background-color: #27ae60;
}

/* Estilos para la página de éxito */
.success-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
}

.success-header {
    text-align: center;
    margin-bottom: 2rem;
}

.success-icon {
    font-size: 4rem;
    color: #2ecc71;
    margin-bottom: 1rem;
}

.registration-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.registration-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.confirmation-section {
    text-align: center;
    padding: 2rem;
}

.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 10px;
    padding: 2rem;
}

.back-btn {
    display: inline-block;
    margin-top: 1rem;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.back-btn:hover {
    background-color: #2980b9;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 1rem;
    border-radius: 5px;
    margin-top: 1rem;
}

/* Estilos para la información de compra */
.purchase-info {
    margin-top: 1rem;
}

.info-box {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.info-box h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.info-box p {
    margin-bottom: 0.5rem;
    color: #555;
}

/* Estilos para la barra de progreso */
.progress-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
}

.progress-bar-wrapper {
    width: 300px;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.progress-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

#progress-percentage {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

#progress-status {
    font-size: 1rem;
    color: #666;
    font-style: italic;
}

/* Estilos para el proceso de compra */
.purchase-process {
    max-width: 600px;
    margin: 30px auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.purchase-step {
    margin-bottom: 30px;
}

.purchase-step h2 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.registration-form .form-group {
    margin-bottom: 15px;
}

.registration-form label {
    display: block;
    margin-bottom: 5px;
    color: #34495e;
}

.registration-form input,
.registration-form textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
}

.registration-form textarea {
    height: 100px;
    resize: vertical;
}

.process-btn {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.1em;
    margin-top: 20px;
    transition: background-color 0.3s ease;
}

.process-btn:hover {
    background-color: #2980b9;
}

/* Estilos para el elemento de tarjeta de Stripe */
#card-element {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

#card-errors {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 0.9em;
}

@media (max-width: 480px) {
    .container {
        padding: 1rem;
    }

    .settings-container {
        flex-direction: column;
    }

    .setting {
        width: 100%;
        margin-bottom: 1rem;
    }
}