Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9CF0, 0007FFFF8BF0) msys-2.0.dll+0x1FEBA
0007FFFF9CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x67F9
0007FFFF9CF0  000210046832 (000210285FF9, 0007FFFF9BA8, 0007FFFF9CF0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CF0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9CF0  0002100690B4 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9FD0  00021006A49D (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCC79C0000 ntdll.dll
7FFCC5CE0000 KERNEL32.DLL
7FFCC4BC0000 KERNELBASE.dll
7FFCC15E0000 apphelp.dll
7FFCC59C0000 USER32.dll
7FFCC56A0000 win32u.dll
7FFCC6210000 GDI32.dll
7FFCC4FB0000 gdi32full.dll
7FFCC4B10000 msvcp_win.dll
7FFCC53D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCC6010000 advapi32.dll
7FFCC6E70000 msvcrt.dll
7FFCC5870000 sechost.dll
7FFCC68C0000 RPCRT4.dll
7FFCC4210000 CRYPTBASE.DLL
7FFCC50F0000 bcryptPrimitives.dll
7FFCC61D0000 IMM32.DLL
