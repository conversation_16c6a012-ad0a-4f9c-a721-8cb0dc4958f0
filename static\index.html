<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hilos - Thread Image Generator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/styles.css">
</head>

<body>
    <div class="container">
        <header>
            <h1>Hilos <span>Thread Image Generator</span></h1>
        </header>

        <main>
            <form>
                <div class="image-upload-container">
                    <input type="file" id="imageInput" accept="image/*" required class="file-input">
                    <label for="imageInput" class="file-input-label">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Selecciona una imagen</span>
                    </label>

                    <div class="preview-container">
                        <img id="preview" alt="Vista previa" class="preview-image">
                    </div>
                </div>

                <div class="settings-container">
                    <div class="setting">
                        <label for="pins">
                            <i class="fas fa-thumbtack"></i> Pins
                        </label>
                        <select id="pins" name="pins">
                            <option value="120">120 Pins</option>
                            <option value="180">180 Pins</option>
                            <option value="200">200 Pins</option>
                            <option value="240" selected>240 Pins</option>
                        </select>
                    </div>
                    <div class="setting">
                        <label for="lines">
                            <i class="fas fa-bezier-curve"></i> Líneas
                        </label>
                        <select id="lines" name="lines">
                            <option value="10" selected>10 Líneas</option>
                            <option value="3000">3000 Líneas</option>
                            <option value="3500">3500 Líneas</option>
                            <option value="4500">4500 Líneas</option>
                            <option value="6000">6000 Líneas</option>
                        </select>
                    </div>
                </div>

                <button id="generateBtn" class="generate-btn" disabled>
                    <i class="fas fa-magic"></i> Generar Imagen de Hilos
                </button>
            </form>

            <div id="status" class="status-container">
                <div id="loading" class="loading">
                    <div class="progress-container">
                        <div class="progress-bar-wrapper">
                            <div id="progress-bar" class="progress-bar"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progress-percentage">0%</span>
                            <span id="progress-status">Iniciando...</span>
                        </div>
                    </div>
                </div>
                <div id="error" class="error"></div>
            </div>

            <div class="result-container">
                <img id="result" alt="Imagen generada" class="result-image">
                <!-- Agregamos el botón de compra -->
                <button id="buyButton" class="buy-btn" style="display: none;">
                    <i class="fas fa-shopping-cart"></i> Comprar esta imagen
                </button>
            </div>

            <!-- Mensaje informativo sobre el proceso de compra -->
            <div id="purchaseInfo" class="purchase-info" style="display: none;">
                <div class="info-box">
                    <h3>🛒 Proceso de Compra</h3>
                    <p>Al hacer clic en "Comprar", serás redirigido a una página segura de Stripe para completar tu
                        pago.</p>
                    <p>Después del pago exitoso, podrás registrar tu información de envío.</p>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/script.js"></script>
</body>

</html>